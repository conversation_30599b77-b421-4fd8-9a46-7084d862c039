'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTheme } from 'next-themes';

interface SimpleCodeRendererProps {
  content: string;
  language?: string;
  className?: string;
  showLineNumbers?: boolean;
}

export function SimpleCodeRenderer({ 
  content, 
  language = '', 
  className,
  showLineNumbers = false 
}: SimpleCodeRendererProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Split content into lines
  const lines = content.split('\n');

  // Get language-specific styling
  const getLanguageClass = (lang: string) => {
    const langMap: Record<string, string> = {
      python: 'language-python',
      py: 'language-python',
      javascript: 'language-javascript',
      js: 'language-javascript',
      typescript: 'language-typescript',
      ts: 'language-typescript',
      java: 'language-java',
      cpp: 'language-cpp',
      c: 'language-c',
      rust: 'language-rust',
      go: 'language-go',
      php: 'language-php',
      ruby: 'language-ruby',
      swift: 'language-swift',
      kotlin: 'language-kotlin',
      scala: 'language-scala',
      r: 'language-r',
      sql: 'language-sql',
      html: 'language-html',
      css: 'language-css',
      json: 'language-json',
      yaml: 'language-yaml',
      xml: 'language-xml',
      markdown: 'language-markdown',
      bash: 'language-bash',
      shell: 'language-shell',
    };
    return langMap[lang.toLowerCase()] || 'language-text';
  };

  // Basic syntax highlighting for Python (simple regex-based)
  const highlightPython = (line: string) => {
    if (language.toLowerCase() !== 'python' && language.toLowerCase() !== 'py') {
      return line;
    }

    // Keywords
    line = line.replace(
      /\b(def|class|if|elif|else|for|while|try|except|finally|with|import|from|as|return|yield|break|continue|pass|lambda|and|or|not|in|is|True|False|None)\b/g,
      '<span class="text-purple-400">$1</span>'
    );

    // Strings
    line = line.replace(
      /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g,
      '<span class="text-green-400">$1$2$1</span>'
    );

    // Comments
    line = line.replace(
      /#.*/g,
      '<span class="text-gray-500">$&</span>'
    );

    // Numbers
    line = line.replace(
      /\b\d+\.?\d*\b/g,
      '<span class="text-blue-400">$&</span>'
    );

    // Function calls
    line = line.replace(
      /\b(\w+)(?=\()/g,
      '<span class="text-yellow-400">$1</span>'
    );

    return line;
  };

  const isDark = mounted && resolvedTheme === 'dark';

  return (
    <ScrollArea className={cn("w-full h-full", className)}>
      <div className="w-full">
        <div className={cn(
          "font-mono text-sm overflow-x-auto",
          isDark ? "bg-[#1e1e1e] text-[#d4d4d4]" : "bg-[#f8f8f8] text-[#333333]",
          getLanguageClass(language)
        )}>
          {showLineNumbers ? (
            <div className="flex">
              {/* Line numbers */}
              <div className={cn(
                "select-none border-r px-2 py-2 text-right min-w-[3rem]",
                isDark ? "bg-[#252526] border-[#3e3e42] text-[#858585]" : "bg-[#f0f0f0] border-[#e0e0e0] text-[#666666]"
              )}>
                {lines.map((_, index) => (
                  <div key={index} className="leading-6">
                    {index + 1}
                  </div>
                ))}
              </div>
              
              {/* Code content */}
              <div className="flex-1 p-2">
                {lines.map((line, index) => (
                  <div 
                    key={index} 
                    className="leading-6 whitespace-pre"
                    dangerouslySetInnerHTML={{ 
                      __html: highlightPython(line) || ' ' 
                    }}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="p-4">
              {lines.map((line, index) => (
                <div 
                  key={index} 
                  className="leading-6 whitespace-pre"
                  dangerouslySetInnerHTML={{ 
                    __html: highlightPython(line) || ' ' 
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  );
}
