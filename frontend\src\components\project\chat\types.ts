export interface Message {
  id: string;
  sender: string; // Allow any agent ID
  content: string;
  timestamp: string;
  isCode?: boolean;
  isEmail?: boolean;
  isGitHub?: boolean;
  gitHubAction?: 'commit' | 'pull-request' | 'merge';
  isFile?: boolean;
  isMultiFile?: boolean;
  fileCount?: number;
  fileName?: string;
  filePath?: string;
  fileSize?: string;
  fileType?: string;
  isSystem?: boolean;
  isCall?: boolean;
  callType?: 'audio' | 'video';
  callStatus?: 'started' | 'ended' | 'missed';
  callDuration?: string;
  calledPerson?: string;
  role?: 'user' | 'assistant';
  type?: 'user' | 'assistant' | 'thinking';
}

export interface LiveProjectChatInterfaceProps {
  isActive: boolean;
  project?: any;
}

export interface CustomGroup {
  id: string;
  name: string;
  participants: Array<{ id: number; name: string; role: string; avatar: string }>;
  messages: Message[];
  threadId: string | null;
}

export interface Employee {
  id: number;
  name: string;
  role: string;
  avatar: string;
}

export interface AgentRole {
  id: string;
  name: string;
  role: string;
  avatar: string;
  type: string;
}

export interface CallParticipant {
  id: string;
  name: string;
  avatar: string;
  role: string;
  isMuted: boolean;
  isVideoOn: boolean;
  isSpeaking: boolean;
}

export interface FileData {
  name: string;
  content: string;
  size: number;
  type: string;
}

export interface SelectedFile {
  name: string;
  content: string | null;
  type: string;
}
