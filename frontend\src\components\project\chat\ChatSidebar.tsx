'use client';

import React from 'react';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Message, CustomGroup } from './types';
import { stripMarkdown, ALL_EMPLOYEES, AGENT_ROLES } from './utils';
import { GroupAvatar } from './GroupAvatar';

interface ChatSidebarProps {
  activeChat: string;
  setActiveChat: (chatId: string) => void;
  ceoMessages: Message[];
  developerMessages: Message[];
  groupMessages: Message[];
  customGroups: CustomGroup[];
  selectedEmployees: number[];
  setSelectedEmployees: (employees: number[]) => void;
  isEmployeePopoverOpen: boolean;
  setIsEmployeePopoverOpen: (open: boolean) => void;
  createGroupChat: () => void;
  projectAgents?: string[];
  toggleAgentSelection?: (agentId: string) => void;
  collaborationMode?: boolean;
  setCollaborationMode?: (enabled: boolean) => void;
}

export function ChatSidebar({
  activeChat,
  setActiveChat,
  ceoMessages,
  developerMessages,
  groupMessages,
  customGroups,
  selectedEmployees,
  setSelectedEmployees,
  isEmployeePopoverOpen,
  setIsEmployeePopoverOpen,
  createGroupChat,
  projectAgents,
  toggleAgentSelection,
  collaborationMode,
  setCollaborationMode
}: ChatSidebarProps) {
  // Debug log to see current activeChat value
  console.log('ChatSidebar rendering with activeChat:', activeChat, 'and projectAgents:', projectAgents);
  return (
    <div className="w-72 border-r border-border flex flex-col">
      <div className="h-[60px] px-4 flex items-center justify-between border-b border-border">
        <h2 className="text-white font-semibold text-lg">Chat</h2>
        <div className="flex border border-[#333333] rounded-md overflow-hidden">
          <Popover open={isEmployeePopoverOpen} onOpenChange={setIsEmployeePopoverOpen}>
            <PopoverTrigger asChild>
              <button
                className="h-8 w-8 flex items-center justify-center bg-[#222222] hover:bg-[#333333] text-[#999999] hover:text-white"
              >
                <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 5v14M5 12h14" />
                </svg>
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4 bg-[#1a1a1a] border-[#333333] rounded-md" align="end" side="bottom">
              <Tabs defaultValue="employees" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger value="employees">Employees</TabsTrigger>
                  <TabsTrigger value="agents">Agents</TabsTrigger>
                </TabsList>
                
                <TabsContent value="employees" className="space-y-4">
                  <div>
                    <h3 className="text-base font-semibold text-white mb-2">Select Employees</h3>
                    <p className="text-sm text-[#999999] mb-4">You can add up to 5 employees.</p>

                    <div className="space-y-2 max-h-[250px] overflow-y-auto pr-1">
                      {ALL_EMPLOYEES.map(employee => (
                        <div
                          key={employee.id}
                          className="flex items-center justify-between py-2 px-3 hover:bg-[#222222] rounded-md cursor-pointer"
                          onClick={() => {
                            if (selectedEmployees.includes(employee.id)) {
                              setSelectedEmployees(selectedEmployees.filter(id => id !== employee.id));
                            } else {
                              setSelectedEmployees([...selectedEmployees, employee.id]);
                            }
                          }}
                        >
                          <div className="flex items-center">
                            <div className="mr-3">
                              <div className="h-9 w-9 rounded-md overflow-hidden">
                                <Image src={employee.avatar} alt={employee.name} width={36} height={36} className="h-full w-full object-cover" />
                              </div>
                            </div>
                            <div>
                              <div className="text-base font-medium text-white">{employee.name}</div>
                              <div className="text-sm text-[#999999]">{employee.role}</div>
                            </div>
                          </div>
                          <div className={`h-6 w-6 rounded-md flex items-center justify-center ${selectedEmployees.includes(employee.id) ? 'bg-[#5865F2] border-[#5865F2]' : 'border border-[#444444] bg-[#222222]'}`}>
                            {selectedEmployees.includes(employee.id) && (
                              <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="20 6 9 17 4 12" />
                              </svg>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <Button
                    className="w-full py-2 bg-[#5865F2] hover:bg-[#4752C4] text-white rounded-md font-medium text-base"
                    onClick={createGroupChat}
                    disabled={selectedEmployees.length === 0}
                  >
                    Create Group
                  </Button>
                </TabsContent>

                <TabsContent value="agents" className="space-y-4">
                  <div>
                    <h3 className="text-base font-semibold text-white mb-2">Project Agents</h3>
                    <p className="text-sm text-[#999999] mb-4">Select which agents will respond in this project.</p>
                    
                    {/* Collaboration Mode Toggle */}
                    <div className="flex items-center justify-between mb-4 p-3 bg-[#222222] rounded-md">
                      <div>
                        <div className="text-sm font-medium text-white">Collaboration Mode</div>
                        <div className="text-xs text-[#999999]">Multiple agents can respond together</div>
                      </div>
                      <Switch
                        checked={collaborationMode || false}
                        onCheckedChange={setCollaborationMode}
                      />
                    </div>

                    <div className="space-y-2 max-h-[200px] overflow-y-auto pr-1">
                      {AGENT_ROLES.map(agent => (
                        <div
                          key={agent.id}
                          className="flex items-center justify-between py-2 px-3 hover:bg-[#222222] rounded-md cursor-pointer"
                          onClick={() => toggleAgentSelection && toggleAgentSelection(agent.id)}
                        >
                          <div className="flex items-center">
                            <div className="mr-3">
                              <div className="h-9 w-9 rounded-md overflow-hidden bg-[#333333] flex items-center justify-center">
                                <span className="text-white text-sm font-medium">
                                  {agent.name.charAt(0)}
                                </span>
                              </div>
                            </div>
                            <div>
                              <div className="text-base font-medium text-white">{agent.name}</div>
                              <div className="text-sm text-[#999999]">{agent.role}</div>
                            </div>
                          </div>
                          <div className={`h-6 w-6 rounded-md flex items-center justify-center ${projectAgents && projectAgents.includes(agent.id) ? 'bg-[#5865F2] border-[#5865F2]' : 'border border-[#444444] bg-[#222222]'}`}>
                            {projectAgents && projectAgents.includes(agent.id) && (
                              <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="20 6 9 17 4 12" />
                              </svg>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    {!collaborationMode && projectAgents && projectAgents.length > 1 && (
                      <div className="p-3 bg-[#2a2a2a] rounded-md">
                        <p className="text-xs text-[#999999]">
                          Only {AGENT_ROLES.find(r => r.id === projectAgents[0])?.name} (the first selected agent) will respond.
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* CEO Chat - Show if ceo is selected */}
        {projectAgents?.includes('ceo') && (
          <div
            key="ceo"
            className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeChat === 'ceo' ? 'bg-accent' : 'hover:bg-muted'}`}
            onClick={() => {
              console.log('Setting activeChat to: ceo');
              setActiveChat('ceo');
            }}
          >
            <div className="flex-shrink-0 h-10 w-10 rounded-md bg-[#4f6bed] mr-3 text-white font-medium overflow-hidden">
              <div className="h-10 w-10 flex items-center justify-center">
                <Image 
                  src={AGENT_ROLES.find(a => a.id === 'ceo')?.avatar || '/roles/kenard.png'} 
                  alt={AGENT_ROLES.find(a => a.id === 'ceo')?.name || 'Kenard'} 
                  width={40} 
                  height={40} 
                  className="h-full w-full object-cover" 
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentNode as HTMLElement;
                    const fallbackEl = parent.querySelector('span');
                    if (fallbackEl) {
                      fallbackEl.textContent = 'K';
                      fallbackEl.classList.remove('hidden');
                    }
                  }}
                />
                <span className="hidden text-white text-lg font-medium">K</span>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="font-medium truncate">{AGENT_ROLES.find(a => a.id === 'ceo')?.name || 'Kenard'}</div>
                <div className="text-xs text-muted-foreground">{ceoMessages.length > 0 ? ceoMessages[ceoMessages.length - 1].timestamp : ''}</div>
              </div>
              <div className="text-sm text-muted-foreground truncate">
                {ceoMessages.length > 0 ? stripMarkdown(ceoMessages[ceoMessages.length - 1].content) : 'No messages yet'}
              </div>
            </div>
          </div>
        )}
        
        {/* Developer Chat - Show if developer is selected */}
        {projectAgents?.includes('developer') && (
          <div
            key="developer"
            className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeChat === 'developer' ? 'bg-accent' : 'hover:bg-muted'}`}
            onClick={() => {
              console.log('Setting activeChat to: developer');
              setActiveChat('developer');
            }}
          >
            <div className="flex-shrink-0 h-10 w-10 rounded-md bg-[#4f6bed] mr-3 text-white font-medium overflow-hidden">
              <div className="h-10 w-10 flex items-center justify-center">
                <Image 
                  src={AGENT_ROLES.find(a => a.id === 'developer')?.avatar || '/roles/alex.png'} 
                  alt={AGENT_ROLES.find(a => a.id === 'developer')?.name || 'Alex'} 
                  width={40} 
                  height={40} 
                  className="h-full w-full object-cover" 
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentNode as HTMLElement;
                    const fallbackEl = parent.querySelector('span');
                    if (fallbackEl) {
                      const name = AGENT_ROLES.find(a => a.id === 'developer')?.name || 'Alex';
                      fallbackEl.textContent = name.charAt(0);
                      fallbackEl.classList.remove('hidden');
                    }
                  }}
                />
                <span className="hidden text-white text-lg font-medium">A</span>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="font-medium truncate">{AGENT_ROLES.find(a => a.id === 'developer')?.name || 'Alex'}</div>
                <div className="text-xs text-muted-foreground">{developerMessages.length > 0 ? developerMessages[developerMessages.length - 1].timestamp : 'Online'}</div>
              </div>
              <div className="text-sm text-muted-foreground truncate">
                {developerMessages.length > 0 ? stripMarkdown(developerMessages[developerMessages.length - 1].content) : 'No messages yet'}
              </div>
            </div>
          </div>
        )}
        
        {/* Other selected agents */}
        {projectAgents?.filter(agentId => 
          agentId !== 'ceo' && 
          agentId !== 'developer'
        ).map(agentId => {
          // Find agent by id
          const agent = AGENT_ROLES.find(a => a.id === agentId);
          if (!agent) return null;
          
          // For all agents, we use their id as the chat key (marketing, product, etc.)
          // Make sure we're using the same format as the activeChat state
          const chatKey = agentId;
          console.log(`Agent ${agent.name} has chatKey: ${chatKey}, activeChat is: ${activeChat}, match: ${activeChat === chatKey}`);
          // Since we don't have specific message arrays for these agents, use empty array as fallback
          const agentMessages: Message[] = [];
          
          return (
            <div
              key={chatKey}
              className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeChat === chatKey ? 'bg-accent' : 'hover:bg-muted'}`}
              onClick={() => {
                console.log(`Setting activeChat to: ${chatKey}`);
                setActiveChat(chatKey);
              }}
            >
              <div className="flex-shrink-0 h-10 w-10 rounded-md bg-[#4f6bed] mr-3 text-white font-medium overflow-hidden">
                <div className="h-10 w-10 flex items-center justify-center">
                  <Image 
                    src={agent.avatar} 
                    alt={agent.name} 
                    width={40} 
                    height={40} 
                    className="h-full w-full object-cover" 
                    onError={(e) => {
                      // Fallback to first letter if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentNode as HTMLElement;
                      const fallbackEl = parent.querySelector('span');
                      if (fallbackEl) {
                        fallbackEl.textContent = agent.name.charAt(0);
                        fallbackEl.classList.remove('hidden');
                      }
                    }}
                  />
                  <span className="hidden text-white text-lg font-medium">{agent.name.charAt(0)}</span>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div className="font-medium truncate">{agent.name}</div>
                  <div className="text-xs text-muted-foreground">{agentMessages.length > 0 ? agentMessages[agentMessages.length - 1].timestamp : 'Online'}</div>
                </div>
                <div className="text-sm text-muted-foreground truncate">
                  {agentMessages.length > 0 ? stripMarkdown(agentMessages[agentMessages.length - 1].content) : 'No messages yet'}
                </div>
              </div>
            </div>
          );
        })}
        
        {/* Team Chat */}
        <div
          className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeChat === 'group' ? 'bg-accent' : 'hover:bg-muted'}`}
          onClick={() => setActiveChat('group')}
        >
          <div className="flex-shrink-0 h-10 w-10 rounded-md bg-[#4f6bed] mr-3 text-white font-medium overflow-hidden">
            <div className="h-10 w-10 flex items-center justify-center">
              <div className="text-white text-lg font-medium">TC</div>
            </div>
          </div>
          <div className="flex-1 min-w-0 flex flex-col justify-center">
            <div className="flex justify-between items-center">
              <p className="text-white font-medium truncate">Team Chat</p>
              <span className="text-xs text-[#999999] flex-shrink-0 ml-2">
                {groupMessages.length > 0 ? groupMessages[groupMessages.length - 1].timestamp : 'Online'}
              </span>
            </div>
            <p className="text-sm text-[#999999] truncate">
              {groupMessages.length > 0
                ? (() => {
                    const strippedContent = stripMarkdown(groupMessages[groupMessages.length - 1].content);
                    return strippedContent.length > 40
                      ? strippedContent.substring(0, 40) + '...'
                      : strippedContent;
                  })()
                : 'No messages yet'}
            </p>
          </div>
        </div>

        {/* Custom Groups */}
        {customGroups.map((group) => (
          <div
            key={group.id}
            className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeChat === group.id ? 'bg-accent' : 'hover:bg-muted'}`}
            onClick={() => setActiveChat(group.id)}
          >
            <div className="flex-shrink-0 mr-3">
              <GroupAvatar participants={group.participants} />
            </div>
            <div className="flex-1 min-w-0 flex flex-col justify-center">
              <div className="flex justify-between items-center">
                <p className="text-white font-medium truncate">{group.name}</p>
                <span className="text-xs text-[#999999] flex-shrink-0 ml-2">
                  {group.messages.length > 0 ? group.messages[group.messages.length - 1].timestamp : 'Online'}
                </span>
              </div>
              <p className="text-sm text-[#999999] truncate">
                {group.messages.length > 0
                  ? (() => {
                      const strippedContent = stripMarkdown(group.messages[group.messages.length - 1].content);
                      return strippedContent.length > 40
                        ? strippedContent.substring(0, 40) + '...'
                        : strippedContent;
                    })()
                  : `${group.participants.length} participants`}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
