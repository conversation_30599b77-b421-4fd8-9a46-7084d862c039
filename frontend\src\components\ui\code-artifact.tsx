'use client';

import React, { useState, useCallback } from 'react';
import { Copy, Play, Download, Maximize2, Minimize2, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SimpleCodeRenderer } from '@/components/ui/simple-code-renderer';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface CodeArtifactProps {
  code: string;
  language?: string;
  title?: string;
  description?: string;
  className?: string;
  onExecute?: (code: string, language: string) => Promise<any>;
  executionResult?: {
    output?: string;
    error?: string;
    images?: string[];
    isLoading?: boolean;
    isSuccess?: boolean;
  };
  showExecuteButton?: boolean;
  showCopyButton?: boolean;
  showDownloadButton?: boolean;
  isExpandable?: boolean;
}

export function CodeArtifact({
  code,
  language = 'python',
  title,
  description,
  className,
  onExecute,
  executionResult,
  showExecuteButton = true,
  showCopyButton = true,
  showDownloadButton = true,
  isExpandable = true
}: CodeArtifactProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success("Copied to clipboard", {
        description: "Code has been copied to your clipboard",
      });
    } catch (err) {
      toast.error("Failed to copy", {
        description: "Could not copy code to clipboard",
      });
    }
  }, [code]);

  const handleDownload = useCallback(() => {
    const fileExtension = getFileExtension(language);
    const fileName = title ? `${title.replace(/\s+/g, '_')}.${fileExtension}` : `code.${fileExtension}`;
    
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success("Downloaded", {
      description: `Code saved as ${fileName}`,
    });
  }, [code, language, title]);

  const handleExecute = useCallback(async () => {
    if (!onExecute || isExecuting) return;
    
    setIsExecuting(true);
    try {
      await onExecute(code, language);
    } catch (error) {
      toast.error("Execution failed", {
        description: "Failed to execute code",
      });
    } finally {
      setIsExecuting(false);
    }
  }, [onExecute, code, language, isExecuting]);

  const getFileExtension = (lang: string): string => {
    const extensions: Record<string, string> = {
      python: 'py',
      javascript: 'js',
      typescript: 'ts',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      rust: 'rs',
      go: 'go',
      php: 'php',
      ruby: 'rb',
      swift: 'swift',
      kotlin: 'kt',
      scala: 'scala',
      r: 'r',
      sql: 'sql',
      html: 'html',
      css: 'css',
      json: 'json',
      yaml: 'yml',
      xml: 'xml',
      markdown: 'md',
      bash: 'sh',
      shell: 'sh',
    };
    return extensions[lang.toLowerCase()] || 'txt';
  };

  const getLanguageDisplayName = (lang: string): string => {
    const names: Record<string, string> = {
      python: 'Python',
      javascript: 'JavaScript',
      typescript: 'TypeScript',
      java: 'Java',
      cpp: 'C++',
      c: 'C',
      rust: 'Rust',
      go: 'Go',
      php: 'PHP',
      ruby: 'Ruby',
      swift: 'Swift',
      kotlin: 'Kotlin',
      scala: 'Scala',
      r: 'R',
      sql: 'SQL',
      html: 'HTML',
      css: 'CSS',
      json: 'JSON',
      yaml: 'YAML',
      xml: 'XML',
      markdown: 'Markdown',
      bash: 'Bash',
      shell: 'Shell',
    };
    return names[lang.toLowerCase()] || lang.toUpperCase();
  };

  return (
    <div className={cn(
      "border border-[#333333] rounded-lg bg-[#1a1a1a] overflow-hidden",
      isExpanded && "fixed inset-4 z-50 bg-[#1a1a1a]",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 bg-[#222222] border-b border-[#333333]">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <div>
            <div className="text-sm font-medium text-white">
              {title || `${getLanguageDisplayName(language)} Code`}
            </div>
            {description && (
              <div className="text-xs text-[#999999]">{description}</div>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {showExecuteButton && language === 'python' && (
            <Button
              size="sm"
              variant="outline"
              onClick={handleExecute}
              disabled={isExecuting || executionResult?.isLoading}
              className="h-7 px-2 text-xs bg-green-600 hover:bg-green-700 border-green-600 text-white"
            >
              <Play className="h-3 w-3 mr-1" />
              {isExecuting || executionResult?.isLoading ? 'Running...' : 'Run'}
            </Button>
          )}
          
          {showCopyButton && (
            <Button
              size="sm"
              variant="outline"
              onClick={handleCopy}
              className="h-7 px-2 text-xs border-[#444444] text-[#cccccc] hover:bg-[#333333]"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
          )}
          
          {showDownloadButton && (
            <Button
              size="sm"
              variant="outline"
              onClick={handleDownload}
              className="h-7 px-2 text-xs border-[#444444] text-[#cccccc] hover:bg-[#333333]"
            >
              <Download className="h-3 w-3 mr-1" />
              Download
            </Button>
          )}
          
          {isExpandable && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-7 px-2 text-xs border-[#444444] text-[#cccccc] hover:bg-[#333333]"
            >
              {isExpanded ? (
                <Minimize2 className="h-3 w-3" />
              ) : (
                <Maximize2 className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Code Content */}
      <div className={cn(
        "relative",
        isExpanded ? "h-[calc(100vh-200px)] overflow-auto" : "max-h-96 overflow-auto"
      )}>
        <SimpleCodeRenderer
          content={code}
          language={language}
          className="h-full"
          showLineNumbers={false}
        />
      </div>

      {/* Execution Results */}
      {executionResult && (
        <div className="border-t border-[#333333] bg-[#0f0f0f]">
          <div className="flex items-center gap-2 px-4 py-2 bg-[#1a1a1a] border-b border-[#333333]">
            {executionResult.isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm text-[#cccccc]">Executing...</span>
              </div>
            ) : executionResult.isSuccess ? (
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-green-400">Execution completed</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-400" />
                <span className="text-sm text-red-400">Execution failed</span>
              </div>
            )}
          </div>
          
          <div className="p-4 space-y-3">
            {/* Text Output */}
            {executionResult.output && (
              <div>
                <div className="text-xs text-[#999999] mb-1">Output:</div>
                <pre className="text-sm text-[#cccccc] font-mono whitespace-pre-wrap bg-[#0a0a0a] p-3 rounded border border-[#333333]">
                  {executionResult.output}
                </pre>
              </div>
            )}
            
            {/* Error Output */}
            {executionResult.error && (
              <div>
                <div className="text-xs text-red-400 mb-1">Error:</div>
                <pre className="text-sm text-red-400 font-mono whitespace-pre-wrap bg-[#0a0a0a] p-3 rounded border border-red-900/30">
                  {executionResult.error}
                </pre>
              </div>
            )}
            
            {/* Generated Images */}
            {executionResult.images && executionResult.images.length > 0 && (
              <div>
                <div className="text-xs text-[#999999] mb-2">Generated Visualizations:</div>
                <div className="grid gap-3">
                  {executionResult.images.map((imageBase64: string, index: number) => (
                    <div key={index} className="bg-[#0a0a0a] border border-[#333333] rounded-md p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-zinc-400">Plot {index + 1}</span>
                        <button 
                          onClick={() => {
                            const newWindow = window.open();
                            if (newWindow) {
                              newWindow.document.write(`
                                <html>
                                  <head><title>Plot ${index + 1}</title></head>
                                  <body style="margin:0;padding:20px;background:#000;display:flex;justify-content:center;align-items:center;min-height:100vh;">
                                    <img src="data:image/png;base64,${imageBase64}" style="max-width:100%;max-height:100%;object-fit:contain;" />
                                  </body>
                                </html>
                              `);
                            }
                          }}
                          className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          Open Full Size
                        </button>
                      </div>
                      <div className="flex justify-center">
                        <img 
                          src={`data:image/png;base64,${imageBase64}`}
                          alt={`Generated plot ${index + 1}`}
                          className="max-w-full h-auto rounded border border-[#444444]"
                          style={{ maxHeight: '400px' }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
