version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --save 60 1 --loglevel warning
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  backend:
    image: ghcr.io/${GITHUB_REPOSITORY}/siden-backend:latest
    ports:
      - "8000:8000"
    volumes:
      - ./backend/.env:/app/.env:ro
    environment:
      - ENV_MODE=local
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_SSL=False
    depends_on:
      redis:
        condition: service_healthy

  frontend:
    image: ghcr.io/${GITHUB_REPOSITORY}/siden-frontend:latest
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/.env.local:/app/.env.local:ro
    environment:
      - NODE_ENV=production
    command: ["npm", "run", "dev"]
    depends_on:
      - backend

volumes:
  redis-data: