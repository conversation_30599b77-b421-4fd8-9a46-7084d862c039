-- PROJECT AGENTS SCHEMA

-- Create agent_types table to store predefined agent types
CREATE TABLE agent_types (
    agent_type_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    description TEXT,
    capabilities JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create project_agents table to track which agents are selected for each project
CREATE TABLE project_agents (
    project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
    agent_type_id UUID NOT NULL REFERENCES agent_types(agent_type_id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    PRIMARY KEY (project_id, agent_type_id)
);

-- Create indexes for better query performance
CREATE INDEX idx_project_agents_project_id ON project_agents(project_id);
CREATE INDEX idx_project_agents_agent_id ON project_agents(agent_id);
CREATE INDEX idx_project_agents_agent_type_id ON project_agents(agent_type_id);

-- Insert predefined agent types
INSERT INTO agent_types (name, role, description, capabilities) VALUES
('Kenard', 'CEO', 'Strategic leadership and decision making', '{"leadership": true, "strategy": true, "decision_making": true}'),
('Alex', 'Developer', 'Software development and technical implementation', '{"coding": true, "debugging": true, "technical_design": true}'),
('Chloe', 'Marketing', 'Marketing strategy and content creation', '{"marketing": true, "content_creation": true, "social_media": true}'),
('Mark', 'Product', 'Product management and user experience', '{"product_management": true, "user_experience": true, "roadmap_planning": true}'),
('Hannah', 'Sales', 'Sales strategy and customer acquisition', '{"sales": true, "negotiation": true, "customer_acquisition": true}'),
('Jenna', 'Finance', 'Financial planning and analysis', '{"finance": true, "budgeting": true, "financial_analysis": true}'),
('Maisie', 'Design', 'Visual design and user interface', '{"design": true, "ui_ux": true, "visual_communication": true}'),
('Garek', 'Research', 'Market research and competitive analysis', '{"research": true, "data_analysis": true, "competitive_analysis": true}');

-- Create function to get available agents for a project
CREATE OR REPLACE FUNCTION get_project_agents(p_project_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    agents_array JSONB;
BEGIN
    WITH project_agent_data AS (
        SELECT 
            pa.project_id,
            pa.agent_id,
            at.name,
            at.role,
            at.description,
            at.capabilities,
            pa.is_active,
            pa.metadata
        FROM 
            project_agents pa
            JOIN agent_types at ON pa.agent_type_id = at.agent_type_id
        WHERE 
            pa.project_id = p_project_id
            AND pa.is_active = TRUE
    )
    SELECT JSONB_AGG(row_to_json(project_agent_data))
    INTO agents_array
    FROM project_agent_data;
    
    -- Handle the case when no agents are found
    IF agents_array IS NULL THEN
        RETURN '[]'::JSONB;
    END IF;
    
    RETURN agents_array;
END;
$$;

-- Create function to check if an agent is available for a project
CREATE OR REPLACE FUNCTION is_agent_available_for_project(p_project_id UUID, p_agent_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    is_available BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM project_agents
        WHERE project_id = p_project_id
        AND agent_id = p_agent_id
        AND is_active = TRUE
    ) INTO is_available;
    
    RETURN is_available;
END;
$$;

-- Create function to check if two agents can communicate
CREATE OR REPLACE FUNCTION can_agents_communicate(p_from_agent_id UUID, p_to_agent_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    common_projects INT;
BEGIN
    -- Count projects where both agents are active
    SELECT COUNT(*)
    INTO common_projects
    FROM project_agents pa1
    JOIN project_agents pa2 ON pa1.project_id = pa2.project_id
    WHERE pa1.agent_id = p_from_agent_id
    AND pa2.agent_id = p_to_agent_id
    AND pa1.is_active = TRUE
    AND pa2.is_active = TRUE;
    
    RETURN common_projects > 0;
END;
$$;