"""
Agent roles and prompt generation for Siden.ai.

This module defines the roles and responsibilities for each agent type,
and provides functions to generate prompts based on the selected agents
for a project.
"""

from typing import Dict, List, Any, Optional
import json

from utils.logger import logger

# Define agent roles and responsibilities
AGENT_ROLES = {
    "Kenard": {
        "role": "CEO",
        "description": "Strategic leadership and decision making",
        "responsibilities": [
            "Set overall vision and strategy",
            "Make high-level decisions",
            "Delegate tasks to appropriate team members",
            "Evaluate progress and adjust strategy as needed",
            "Ensure all team members are working effectively together"
        ],
        "communication_style": "Direct, strategic, and focused on outcomes",
        "delegation_targets": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>are<PERSON>"]
    },
    "Alex": {
        "role": "Developer",
        "description": "Software development and technical implementation",
        "responsibilities": [
            "Write clean, efficient code",
            "Design and implement technical solutions",
            "Debug and fix issues",
            "Optimize performance",
            "Ensure code quality and maintainability"
        ],
        "communication_style": "Technical, precise, and solution-oriented",
        "delegation_targets": ["<PERSON><PERSON>", "<PERSON>are<PERSON>"]
    },
    "Chloe": {
        "role": "Marketing",
        "description": "Marketing strategy and content creation",
        "responsibilities": [
            "Develop marketing strategies",
            "Create compelling content",
            "Manage social media presence",
            "Analyze marketing performance",
            "Identify target audiences and channels"
        ],
        "communication_style": "Creative, persuasive, and audience-focused",
        "delegation_targets": ["Maisie", "Garek"]
    },
    "Mark": {
        "role": "Product",
        "description": "Product management and user experience",
        "responsibilities": [
            "Define product requirements",
            "Create product roadmaps",
            "Prioritize features based on user needs",
            "Ensure excellent user experience",
            "Coordinate between development and design"
        ],
        "communication_style": "User-focused, analytical, and collaborative",
        "delegation_targets": ["Alex", "Maisie", "Garek"]
    },
    "Hannah": {
        "role": "Sales",
        "description": "Sales strategy and customer acquisition",
        "responsibilities": [
            "Develop sales strategies",
            "Identify potential customers",
            "Build and maintain customer relationships",
            "Negotiate deals",
            "Achieve sales targets"
        ],
        "communication_style": "Persuasive, relationship-focused, and results-driven",
        "delegation_targets": ["Chloe", "Garek"]
    },
    "Jenna": {
        "role": "Finance",
        "description": "Financial planning and analysis",
        "responsibilities": [
            "Manage budgets and financial planning",
            "Analyze financial performance",
            "Provide financial insights for decision making",
            "Ensure financial compliance",
            "Optimize resource allocation"
        ],
        "communication_style": "Analytical, precise, and data-driven",
        "delegation_targets": ["Garek"]
    },
    "Maisie": {
        "role": "Design",
        "description": "Visual design and user interface",
        "responsibilities": [
            "Create visual designs for products",
            "Design user interfaces and experiences",
            "Develop brand identity and assets",
            "Ensure design consistency",
            "Collaborate with development and product teams"
        ],
        "communication_style": "Visual, creative, and user-centered",
        "delegation_targets": ["Garek"]
    },
    "Garek": {
        "role": "Research",
        "description": "Market research and competitive analysis",
        "responsibilities": [
            "Conduct market research",
            "Analyze competitors",
            "Identify market trends and opportunities",
            "Provide data-driven insights",
            "Support decision making with research"
        ],
        "communication_style": "Analytical, thorough, and objective",
        "delegation_targets": []
    }
}

def get_agent_role_info(agent_name: str) -> Dict[str, Any]:
    """Get information about an agent's role.

    Args:
        agent_name: Name of the agent

    Returns:
        Dictionary with agent role information
    """
    return AGENT_ROLES.get(agent_name, {})

def generate_agent_prompt(
    agent_name: str,
    project_agents: List[Dict[str, Any]],
    base_prompt: str
) -> str:
    """Generate a prompt for an agent based on its role and the other agents in the project.

    Args:
        agent_name: Name of the agent
        project_agents: List of agents in the project
        base_prompt: Base prompt to extend with role-specific information

    Returns:
        Complete prompt for the agent
    """
    # Get agent role information
    agent_role_info = get_agent_role_info(agent_name)
    if not agent_role_info:
        logger.warning(f"No role information found for agent {agent_name}")
        return base_prompt

    # Filter out the current agent from the project agents
    other_agents = [a for a in project_agents if a.get("name") != agent_name]

    # Generate team information section
    team_info = "\n\n# Your Team\n"
    team_info += f"You are {agent_name}, the {agent_role_info['role']} of the company.\n\n"
    team_info += "## Your Responsibilities\n"
    for resp in agent_role_info.get("responsibilities", []):
        team_info += f"- {resp}\n"

    team_info += "\n## Your Communication Style\n"
    team_info += f"{agent_role_info.get('communication_style', 'Professional and collaborative')}\n"

    # Add information about other agents
    if other_agents:
        team_info += "\n## Other Team Members\n"
        for agent in other_agents:
            agent_name = agent.get("name")
            agent_info = get_agent_role_info(agent_name)
            if agent_info:
                team_info += f"### {agent_name} ({agent_info['role']})\n"
                team_info += f"{agent_info['description']}\n"
                team_info += "Key responsibilities:\n"
                for resp in agent_info.get("responsibilities", [])[:3]:  # Just show top 3 responsibilities
                    team_info += f"- {resp}\n"
                team_info += "\n"

    # Add task delegation guidance
    delegation_targets = agent_role_info.get("delegation_targets", [])
    available_delegation_targets = [t for t in delegation_targets if any(a.get("name") == t for a in other_agents)]

    if available_delegation_targets:
        team_info += "\n## Task Delegation\n"
        team_info += "You can delegate tasks to the following team members based on their expertise:\n"
        for target in available_delegation_targets:
            target_info = get_agent_role_info(target)
            team_info += f"- {target} ({target_info['role']}): {target_info['description']}\n"

    # Add communication protocol
    team_info += "\n## Communication Protocol\n"
    team_info += "When communicating with other agents, follow these guidelines:\n"
    team_info += "1. Use the AgentCommunicationTool to send messages\n"
    team_info += "2. Use structured communication patterns for different types of interactions:\n"
    team_info += "   - Use `delegate_task` to assign tasks to other agents\n"
    team_info += "   - Use `request_information` to ask questions\n"
    team_info += "   - Use `provide_information` to answer questions\n"
    team_info += "   - Use `respond_to_task` to update task status\n"
    team_info += "3. Always check if you can communicate with an agent using `can_communicate_with` before sending messages\n"
    team_info += "4. Get information about other agents using `get_project_agents`\n"
    team_info += "5. Get role-specific communication guidance using `get_communication_guidance`\n"

    # Add role-specific communication guidance
    from agentpress.communication_patterns import get_role_specific_communication_guidance
    guidance = get_role_specific_communication_guidance(agent_role_info.get('role', ''))
    if guidance:
        team_info += "\n## Role-Specific Communication Guidance\n"
        team_info += f"Communication style: {guidance.get('communication_style', 'Professional and collaborative')}\n\n"
        team_info += "Preferred communication patterns:\n"
        for pattern in guidance.get('preferred_patterns', []):
            team_info += f"- {pattern}\n"
        team_info += "\nGuidance:\n"
        for tip in guidance.get('guidance', []):
            team_info += f"- {tip}\n"

    # Combine with base prompt
    complete_prompt = base_prompt + team_info

    return complete_prompt

def get_agent_type_id(agent_name: str) -> Optional[str]:
    """Get the agent type ID for an agent name.

    This is a placeholder function. In a real implementation, this would
    query the database to get the agent type ID.

    Args:
        agent_name: Name of the agent

    Returns:
        Agent type ID or None if not found
    """
    # This would be replaced with a database query in a real implementation
    agent_type_ids = {
        "Kenard": "00000000-0000-0000-0000-000000000001",
        "Alex": "00000000-0000-0000-0000-000000000002",
        "Chloe": "00000000-0000-0000-0000-000000000003",
        "Mark": "00000000-0000-0000-0000-000000000004",
        "Hannah": "00000000-0000-0000-0000-000000000005",
        "Jenna": "00000000-0000-0000-0000-000000000006",
        "Maisie": "00000000-0000-0000-0000-000000000007",
        "Garek": "00000000-0000-0000-0000-000000000008"
    }
    return agent_type_ids.get(agent_name)